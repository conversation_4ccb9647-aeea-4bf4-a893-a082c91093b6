const axios = require('axios');
require('dotenv').config();

// Configuration de base
const API_BASE_URL = 'http://localhost:3000';

// Fonction pour tester l'API
async function testAPI() {
    console.log('🧪 Test de l\'API Gemini...\n');
    
    try {
        // Test 1: Vérifier si le serveur est en ligne
        console.log('📡 Test 1: Vérification du serveur...');
        const healthResponse = await axios.get(`${API_BASE_URL}/api/test`);
        console.log('✅ Serveur en ligne:', healthResponse.data);
        console.log('');
        
        // Test 2: Test avec une question juridique simple
        console.log('⚖️  Test 2: Question juridique simple...');
        const simpleQuestion = {
            message: "Quelles sont les conditions pour divorcer en Tunisie ?"
        };
        
        const simpleResponse = await axios.post(`${API_BASE_URL}/api/chat`, simpleQuestion);
        console.log('Question:', simpleQuestion.message);
        console.log('Réponse:', simpleResponse.data.response.substring(0, 200) + '...');
        console.log('');
        
        // Test 3: Test avec une question plus complexe
        console.log('🏠 Test 3: Question immobilière...');
        const complexQuestion = {
            message: "Mon propriétaire veut augmenter mon loyer de 50%. Est-ce légal ? Que puis-je faire ?"
        };
        
        const complexResponse = await axios.post(`${API_BASE_URL}/api/chat`, complexQuestion);
        console.log('Question:', complexQuestion.message);
        console.log('Réponse:', complexResponse.data.response.substring(0, 200) + '...');
        console.log('');
        
        // Test 4: Test avec une question vague
        console.log('❓ Test 4: Question vague...');
        const vagueQuestion = {
            message: "J'ai un problème"
        };
        
        const vagueResponse = await axios.post(`${API_BASE_URL}/api/chat`, vagueQuestion);
        console.log('Question:', vagueQuestion.message);
        console.log('Réponse:', vagueResponse.data.response.substring(0, 200) + '...');
        console.log('');
        
        // Test 5: Test avec un message vide
        console.log('🚫 Test 5: Message vide...');
        try {
            const emptyResponse = await axios.post(`${API_BASE_URL}/api/chat`, { message: "" });
            console.log('Réponse inattendue:', emptyResponse.data);
        } catch (error) {
            console.log('✅ Erreur attendue pour message vide:', error.response.data.response);
        }
        console.log('');
        
        // Test 6: Test avec un message trop long
        console.log('📏 Test 6: Message trop long...');
        const longMessage = "A".repeat(2001);
        try {
            const longResponse = await axios.post(`${API_BASE_URL}/api/chat`, { message: longMessage });
            console.log('Réponse inattendue:', longResponse.data);
        } catch (error) {
            console.log('✅ Erreur attendue pour message trop long:', error.response.data.response);
        }
        console.log('');
        
        console.log('🎉 Tous les tests sont terminés !');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Assurez-vous que le serveur Node.js est démarré avec: node server.js');
        } else if (error.response) {
            console.log('Détails de l\'erreur:', error.response.data);
        }
    }
}

// Fonction pour tester avec curl (pour copier-coller)
function generateCurlCommands() {
    console.log('\n📋 Commandes curl pour tester manuellement:\n');
    
    console.log('1. Test du serveur:');
    console.log(`curl -X GET ${API_BASE_URL}/api/test\n`);
    
    console.log('2. Test d\'une question juridique:');
    console.log(`curl -X POST ${API_BASE_URL}/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "Quelles sont les conditions pour divorcer en Tunisie ?"}'`);
    console.log('');
    
    console.log('3. Test avec Postman:');
    console.log(`URL: ${API_BASE_URL}/api/chat`);
    console.log('Method: POST');
    console.log('Headers: Content-Type: application/json');
    console.log('Body: {"message": "Je peux divorcer ?"}');
    console.log('');
}

// Exécuter les tests
if (require.main === module) {
    testAPI().then(() => {
        generateCurlCommands();
        process.exit(0);
    }).catch((error) => {
        console.error('❌ Échec des tests:', error.message);
        process.exit(1);
    });
}

module.exports = { testAPI, generateCurlCommands };
