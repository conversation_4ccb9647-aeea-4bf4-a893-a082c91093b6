<?php

// Script pour vérifier et corriger les permissions de stockage

echo "Vérification des permissions de stockage...\n";

// Vérifier si le dossier storage existe
if (!is_dir('storage')) {
    echo "Erreur: Le dossier 'storage' n'existe pas.\n";
    exit(1);
}

// Vérifier si le dossier storage/app existe
if (!is_dir('storage/app')) {
    echo "Erreur: Le dossier 'storage/app' n'existe pas.\n";
    exit(1);
}

// Vérifier si le dossier storage/app/public existe
if (!is_dir('storage/app/public')) {
    echo "Création du dossier 'storage/app/public'...\n";
    mkdir('storage/app/public', 0755, true);
}

// Vérifier si le dossier storage/app/public/documents existe
if (!is_dir('storage/app/public/documents')) {
    echo "Création du dossier 'storage/app/public/documents'...\n";
    mkdir('storage/app/public/documents', 0755, true);
}

// Vérifier si le dossier storage/logs existe
if (!is_dir('storage/logs')) {
    echo "Création du dossier 'storage/logs'...\n";
    mkdir('storage/logs', 0755, true);
}

// Vérifier si le fichier storage/logs/laravel.log existe
if (!file_exists('storage/logs/laravel.log')) {
    echo "Création du fichier 'storage/logs/laravel.log'...\n";
    file_put_contents('storage/logs/laravel.log', '');
}

// Définir les permissions
echo "Définition des permissions...\n";

// Fonction récursive pour définir les permissions
function chmod_r($path, $filemode, $dirmode) {
    if (is_dir($path)) {
        if (!chmod($path, $dirmode)) {
            echo "Impossible de définir les permissions sur le dossier: $path\n";
        }
        $dh = opendir($path);
        while (($file = readdir($dh)) !== false) {
            if ($file != '.' && $file != '..') {
                $fullpath = $path . '/' . $file;
                chmod_r($fullpath, $filemode, $dirmode);
            }
        }
        closedir($dh);
    } elseif (is_file($path)) {
        if (!chmod($path, $filemode)) {
            echo "Impossible de définir les permissions sur le fichier: $path\n";
        }
    }
}

// Définir les permissions sur le dossier storage
chmod_r('storage', 0644, 0755);

echo "Vérification des permissions terminée.\n";
echo "Assurez-vous que le serveur web a les droits d'écriture sur le dossier 'storage'.\n";
