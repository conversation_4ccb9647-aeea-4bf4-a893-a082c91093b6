<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ConseilJuridique extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'question',
        'response',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);  // Lien avec l'utilisateur (citoyen, avocat)
    }
}
