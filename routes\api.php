<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PlainteController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ConseilJuridiqueControllerV2;

// CORS Pre-flight Options
Route::options('/{any}', function () {
    return response()->noContent()
        ->header('Access-Control-Allow-Origin', 'http://localhost:4200')
        ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        ->header('Access-Control-Allow-Credentials', 'true')
        ->header('Access-Control-Max-Age', '86400');
})->where('any', '.*');

// Public API Routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::get('/test', fn() => response()->json(['message' => 'API is working']));

// Public Plaintes Route - Pour permettre aux utilisateurs non authentifiés de soumettre des plaintes
Route::post('/public/plaintes', [PlainteController::class, 'storePublic']);

// Public Conseil Juridique Route - Pour permettre aux utilisateurs non authentifiés d'obtenir des conseils
Route::post('/public/conseil', [ConseilJuridiqueControllerV2::class, 'generateAdvice']);

// Protected API Routes
Route::middleware('auth:sanctum')->group(function () {
    // User routes
    Route::get('/user', fn(Request $request) => $request->user());
    Route::get('/dashboard', [DashboardController::class, 'index']);

    // Plaintes routes
    Route::get('/plaintes', [PlainteController::class, 'index']);
    Route::post('/plaintes', [PlainteController::class, 'store']);
    Route::get('/plaintes/{id}', [PlainteController::class, 'show']);
    Route::put('/plaintes/{id}', [PlainteController::class, 'update']);
    Route::delete('/plaintes/{id}', [PlainteController::class, 'destroy']);
    Route::post('/plaintes/{id}/documents', [PlainteController::class, 'storeDocument']);

    // Documents routes
    Route::get('/documents/{document}/download', [DocumentController::class, 'download']);

    // Conseil Juridique routes (authentifiées)
    Route::post('/conseil', [ConseilJuridiqueControllerV2::class, 'generateAdvice']);
    Route::get('/conseil/history', [ConseilJuridiqueControllerV2::class, 'getHistory']);
    Route::get('/conseil/{id}', [ConseilJuridiqueControllerV2::class, 'show']);
});
