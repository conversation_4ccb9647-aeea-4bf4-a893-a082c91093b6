<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plaintes', function (Blueprint $table) {
            // Ajout des colonnes manquantes
            $table->string('type')->after('user_id');
            $table->string('subject')->after('type');
            $table->date('incident_date')->nullable()->after('description');
            $table->string('assigned_to_type')->nullable()->after('status');
            $table->unsignedBigInteger('assigned_to_id')->nullable()->after('assigned_to_type');
            
            // Modification du status par défaut
            $table->string('status')->default('nouvelle')->change();
            
            // Index pour la relation polymorphique
            $table->index(['assigned_to_type', 'assigned_to_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plaintes', function (Blueprint $table) {
            $table->dropIndex(['assigned_to_type', 'assigned_to_id']);
            $table->dropColumn(['type', 'subject', 'incident_date', 'assigned_to_type', 'assigned_to_id']);
            $table->string('status')->default('en_attente')->change();
        });
    }
};
