<?php

namespace App\Http\Controllers;

use App\Models\ConseilJuridique;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConseilJuridiqueControllerV2 extends Controller
{
    /**
     * Construire un prompt optimisé pour l'API Gemini
     */
    private function buildPrompt($question, $context = null)
    {
        $basePrompt = "Vous êtes un expert en droit tunisien et français. Répondez en français de manière précise et structurée.\n\n";
        $basePrompt .= "Question: " . $question . "\n\n";
        $basePrompt .= "Instructions:\n";
        $basePrompt .= "- Répondez uniquement sur le plan juridique\n";
        $basePrompt .= "- Citez les articles de loi pertinents\n";
        $basePrompt .= "- Proposez des démarches concrètes\n";
        $basePrompt .= "- Ne donnez pas de conseil personnalisé\n";

        if ($context) {
            $basePrompt .= "\nContexte supplémentaire:\n" . $context;
        }

        return $basePrompt;
    }

    /**
     * Générer un conseil juridique en utilisant l'API Gemini
     */
    public function generateAdvice(Request $request)
    {
        $validated = $request->validate([
            'question' => 'required|string|min:10|max:2000',
            'user_id' => 'nullable|exists:users,id',
            'context' => 'nullable|string|max:1000'
        ]);

        try {
            $prompt = $this->buildPrompt($validated['question'], $validated['context'] ?? null);

            $response = $this->callLegalApi($prompt);

            $conseil = ConseilJuridique::create([
                'user_id' => $validated['user_id'] ?? null,
                'question' => $validated['question'],
                'response' => $response,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $conseil->id,
                    'question' => $conseil->question,
                    'response' => $response,
                    'created_at' => $conseil->created_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Legal advice error', [
                'error' => $e->getMessage(),
                'question' => $validated['question'] ?? 'N/A'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Impossible de générer une réponse pour le moment.',
                'technical' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Obtenir l'historique des conseils pour un utilisateur
     */
    public function getHistory(Request $request)
    {
        $userId = $request->user()->id ?? null;

        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non authentifié'
            ], 401);
        }

        $conseils = ConseilJuridique::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'conseils' => $conseils
        ]);
    }

    /**
     * Obtenir un conseil spécifique
     */
    public function show($id)
    {
        $conseil = ConseilJuridique::findOrFail($id);

        return response()->json([
            'success' => true,
            'conseil' => $conseil
        ]);
    }

    /**
     * Appeler l'API Node.js/Gemini pour obtenir un conseil juridique
     */
    private function callLegalApi($prompt)
    {
        try {
            $response = Http::timeout(30)
                ->retry(3, 500)
                ->post(config('services.legal_api.url', env('NODE_API_URL', 'http://localhost:3000') . '/api/chat'), [
                    'message' => $prompt,
                    'temperature' => 0.3
                ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    return $this->formatResponse($data['response']);
                }
            }

            throw new \Exception('API response error: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('Legal API call failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Formater la réponse pour améliorer la présentation
     */
    private function formatResponse($text)
    {
        // Amélioration de la mise en forme
        $text = preg_replace('/Article ([0-9]+)/', '<strong>Article $1</strong>', $text);
        $text = nl2br($text);

        return $text;
    }
}
