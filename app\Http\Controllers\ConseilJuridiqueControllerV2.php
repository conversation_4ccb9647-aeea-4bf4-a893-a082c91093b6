<?php

namespace App\Http\Controllers;

use App\Models\ConseilJuridique;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConseilJuridiqueControllerV2 extends Controller
{
    /**
     * Générer un conseil juridique en utilisant l'API Gemini
     */
    public function generateAdvice(Request $request)
    {
        // Validation de la requête
        $validated = $request->validate([
            'question' => 'required|string|max:2000',
            'user_id' => 'nullable|exists:users,id',
        ]);

        try {
            Log::info('Génération de conseil juridique:', ['question' => $validated['question']]);

            // Appeler l'API Node.js/Gemini
            $advice = $this->getLegalAdviceFromGemini($validated['question']);

            // Sauvegarder dans la base de données
            $conseil = ConseilJuridique::create([
                'user_id' => $validated['user_id'] ?? null,
                'question' => $validated['question'],
                'response' => $advice,
            ]);

            Log::info('Conseil juridique généré:', ['conseil_id' => $conseil->id]);

            return response()->json([
                'success' => true,
                'conseil' => $conseil,
                'message' => 'Conseil juridique généré avec succès'
            ], 201);

        } catch (\Exception $e) {
            Log::error('Erreur génération conseil juridique:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la génération du conseil juridique.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir l'historique des conseils pour un utilisateur
     */
    public function getHistory(Request $request)
    {
        $userId = $request->user()->id ?? null;
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non authentifié'
            ], 401);
        }

        $conseils = ConseilJuridique::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'conseils' => $conseils
        ]);
    }

    /**
     * Obtenir un conseil spécifique
     */
    public function show($id)
    {
        $conseil = ConseilJuridique::findOrFail($id);
        
        return response()->json([
            'success' => true,
            'conseil' => $conseil
        ]);
    }

    /**
     * Appeler l'API Node.js/Gemini pour obtenir un conseil juridique
     */
    private function getLegalAdviceFromGemini($question)
    {
        try {
            // URL de l'API Node.js
            $nodeApiUrl = env('NODE_API_URL', 'http://localhost:3000') . '/api/chat';
            
            Log::info('Appel API Node.js:', ['url' => $nodeApiUrl]);

            // Faire l'appel HTTP vers l'API Node.js
            $response = Http::timeout(30)->post($nodeApiUrl, [
                'message' => $question
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['success']) && $data['success'] && isset($data['response'])) {
                    return $data['response'];
                } else {
                    throw new \Exception('Réponse invalide de l\'API Gemini: ' . json_encode($data));
                }
            } else {
                throw new \Exception('Erreur HTTP ' . $response->status() . ': ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Erreur appel API Gemini:', ['error' => $e->getMessage()]);
            
            // Fallback: réponse générique
            return "Je suis désolé, mais je ne peux pas traiter votre question pour le moment. " .
                   "Veuillez réessayer plus tard ou consulter un avocat pour obtenir des conseils juridiques personnalisés. " .
                   "Votre question était: " . $question;
        }
    }
}
