<?php

// Script pour vérifier les limites de téléchargement PHP

echo "Vérification des limites de téléchargement PHP...\n";

// Récupérer les limites de téléchargement
$upload_max_filesize = ini_get('upload_max_filesize');
$post_max_size = ini_get('post_max_size');
$max_file_uploads = ini_get('max_file_uploads');
$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');

// Convertir les valeurs en octets pour comparaison
function convert_to_bytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int)$value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}

$upload_max_filesize_bytes = convert_to_bytes($upload_max_filesize);
$post_max_size_bytes = convert_to_bytes($post_max_size);
$memory_limit_bytes = convert_to_bytes($memory_limit);

// Afficher les limites
echo "upload_max_filesize: $upload_max_filesize ($upload_max_filesize_bytes octets)\n";
echo "post_max_size: $post_max_size ($post_max_size_bytes octets)\n";
echo "max_file_uploads: $max_file_uploads\n";
echo "memory_limit: $memory_limit ($memory_limit_bytes octets)\n";
echo "max_execution_time: $max_execution_time secondes\n";

// Vérifier si post_max_size est supérieur à upload_max_filesize
if ($post_max_size_bytes < $upload_max_filesize_bytes) {
    echo "ATTENTION: post_max_size ($post_max_size) est inférieur à upload_max_filesize ($upload_max_filesize).\n";
    echo "Cela peut causer des problèmes lors du téléchargement de fichiers volumineux.\n";
    echo "Il est recommandé de définir post_max_size à une valeur supérieure à upload_max_filesize.\n";
}

// Vérifier si memory_limit est suffisant
if ($memory_limit_bytes < $post_max_size_bytes) {
    echo "ATTENTION: memory_limit ($memory_limit) est inférieur à post_max_size ($post_max_size).\n";
    echo "Cela peut causer des problèmes lors du traitement de requêtes volumineuses.\n";
    echo "Il est recommandé de définir memory_limit à une valeur supérieure à post_max_size.\n";
}

// Recommandations
echo "\nRecommandations pour les téléchargements de fichiers volumineux:\n";
echo "1. Définir upload_max_filesize à au moins 20M\n";
echo "2. Définir post_max_size à au moins 25M\n";
echo "3. Définir memory_limit à au moins 128M\n";
echo "4. Définir max_execution_time à au moins 120 secondes\n";

echo "\nPour modifier ces valeurs, vous pouvez:\n";
echo "1. Modifier le fichier php.ini\n";
echo "2. Ajouter des directives dans le fichier .htaccess\n";
echo "3. Utiliser ini_set() dans votre code PHP (certaines directives ne peuvent pas être modifiées à l'exécution)\n";

echo "\nExemple de directives .htaccess:\n";
echo "php_value upload_max_filesize 20M\n";
echo "php_value post_max_size 25M\n";
echo "php_value memory_limit 128M\n";
echo "php_value max_execution_time 120\n";
echo "php_value max_input_time 120\n";
