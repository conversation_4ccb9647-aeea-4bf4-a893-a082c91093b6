const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const app = express();
const PORT = process.env.NODE_PORT || 3000;

// Configuration CORS simplifiée
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:4200',
    methods: ['GET', 'POST']
}));

// Middleware pour parser le JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialiser l'API Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Prompt système amélioré pour le contexte juridique
const SYSTEM_PROMPT = `
Vous êtes un assistant juridique expert en droit tunisien et français.
Vous répondez exclusivement en français avec un style professionnel.

Votre mission est de:
1. Fournir des informations juridiques précises et actualisées
2. Citer les articles de loi pertinents
3. Proposer des démarches concrètes
4. Recommander de consulter un avocat pour les cas complexes

Domaines d'expertise:
- Droit de la famille (mariage, divorce, héritage)
- Droit du travail (contrats, licenciement, discrimination)
- Droit immobilier (baux, copropriété, construction)
- Droit des contrats (formation, exécution, résiliation)
- Procédures judiciaires (délais, compétences, recours)

Instructions strictes:
- Ne jamais donner de conseil personnalisé
- Toujours citer vos sources quand possible
- Demander des précisions si la question est trop vague
- Rester dans le cadre juridique
- Structurer clairement vos réponses

Format de réponse attendu:
1. Principes juridiques applicables
2. Textes de loi pertinents
3. Démarches recommandées
4. Mises en garde éventuelles
5. Recommandation de consulter un professionnel si nécessaire
`;

// Fonction améliorée pour appeler l'API Gemini
async function fetchGeminiResponse(userMessage) {
    try {
        console.log('📝 Question reçue:', userMessage);

        const model = genAI.getGenerativeModel({
            model: "gemini-pro",
            generationConfig: {
                temperature: 0.3, // Réduit la créativité pour plus de précision
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 2000
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_NONE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_NONE"
                }
            ]
        });

        // Construction du prompt avec historique de conversation
        const chat = model.startChat({
            history: [
                {
                    role: "user",
                    parts: [{ text: SYSTEM_PROMPT }]
                },
                {
                    role: "model",
                    parts: [{ text: "Je suis prêt à répondre à vos questions juridiques. Posez-moi votre question en étant aussi précis que possible." }]
                }
            ]
        });

        const result = await chat.sendMessage(userMessage);
        const response = result.response;
        const text = response.text();

        console.log('✅ Réponse générée:', text.substring(0, 100) + '...');
        return text;

    } catch (error) {
        console.error('❌ Erreur API Gemini:', error);

        // Gestion des erreurs améliorée
        if (error.message.includes('API_KEY')) {
            throw new Error('Problème d\'authentification avec l\'API Gemini');
        } else if (error.message.includes('quota')) {
            throw new Error('Limite d\'utilisation de l\'API atteinte');
        } else if (error.message.includes('safety')) {
            throw new Error('La question a déclenché les filtres de sécurité');
        } else {
            throw new Error('Erreur technique lors de la génération de la réponse');
        }
    }
}

// Route améliorée pour le chat
app.post('/api/chat', async (req, res) => {
    try {
        const { message, conversation_history = [] } = req.body;

        // Validation renforcée
        if (!message || typeof message !== 'string' || message.trim().length < 5) {
            return res.status(400).json({
                success: false,
                response: "Votre question doit contenir au moins 5 caractères."
            });
        }

        if (message.length > 2000) {
            return res.status(400).json({
                success: false,
                response: "Votre question est trop longue (max 2000 caractères)."
            });
        }

        // Appel à Gemini avec gestion du contexte
        const geminiResponse = await fetchGeminiResponse(message);

        // Formatage de la réponse
        const formattedResponse = geminiResponse
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Mise en forme markdown
            .replace(/\n/g, '<br>');

        res.json({
            success: true,
            response: formattedResponse,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ Erreur dans /api/chat:', error);

        res.status(500).json({
            success: false,
            response: "Désolé, je ne peux pas répondre pour le moment. Veuillez reformuler votre question ou réessayer plus tard.",
            error_details: process.env.NODE_ENV === 'development' ? error.message : undefined,
            timestamp: new Date().toISOString()
        });
    }
});

// Route de test
app.get('/api/test', (req, res) => {
    res.json({
        success: true,
        message: 'Serveur Node.js pour l\'assistant juridique opérationnel',
        timestamp: new Date().toISOString(),
        geminiConfigured: !!process.env.GEMINI_API_KEY
    });
});

// Route de santé
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        service: 'Legal Assistant API',
        timestamp: new Date().toISOString()
    });
});

// Middleware de gestion d'erreurs globales
app.use((error, req, res, next) => {
    console.error('❌ Erreur globale:', error);
    res.status(500).json({
        success: false,
        message: 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
    });
});

// Middleware pour les routes non trouvées
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route non trouvée',
        timestamp: new Date().toISOString()
    });
});

// Démarrer le serveur
app.listen(PORT, () => {
    console.log(`🚀 Serveur Node.js démarré sur le port ${PORT}`);
    console.log(`📡 API disponible sur http://localhost:${PORT}/api/chat`);
    console.log(`🔑 Clé Gemini configurée: ${process.env.GEMINI_API_KEY ? 'Oui' : 'Non'}`);

    if (!process.env.GEMINI_API_KEY) {
        console.warn('⚠️  ATTENTION: Clé API Gemini manquante. Ajoutez GEMINI_API_KEY dans votre fichier .env');
    }
});

module.exports = app;
