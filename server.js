const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const app = express();
const PORT = process.env.NODE_PORT || 3000;

// Configuration CORS
app.use(cors({
    origin: ['http://localhost:4200', 'http://localhost:8000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware pour parser le JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialiser l'API Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Prompt système pour le contexte juridique
const SYSTEM_PROMPT = `
Tu es un assistant juridique intelligent spécialisé dans le droit tunisien et français. 
Réponds toujours en français de manière claire et professionnelle.

Tes domaines d'expertise incluent :
- Droit de la famille (divorce, mariage, garde d'enfants)
- Droit du travail (contrats, licenciements, congés)
- Droit des contrats (formation, exécution, résiliation)
- Droit de la consommation (garanties, réclamations)
- Droit immobilier (vente, location, copropriété)
- Procédures judiciaires et administratives

Instructions importantes :
1. Donne des informations juridiques générales, pas de conseils personnalisés
2. Recommande toujours de consulter un avocat pour des cas spécifiques
3. Cite les articles de loi pertinents quand possible
4. Si la question est vague, demande des précisions
5. Reste dans ton domaine de compétence juridique
6. Sois empathique mais professionnel

Format de réponse :
- Explication claire du principe juridique
- Références légales si applicable
- Étapes à suivre ou démarches recommandées
- Recommandation de consultation d'un professionnel si nécessaire
`;

// Fonction pour appeler l'API Gemini
async function fetchGeminiResponse(userMessage) {
    try {
        console.log('📝 Question reçue:', userMessage);
        
        // Utiliser le modèle Gemini Pro
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        
        // Construire le prompt complet
        const fullPrompt = `${SYSTEM_PROMPT}\n\nQuestion de l'utilisateur: ${userMessage}`;
        
        // Générer la réponse
        const result = await model.generateContent(fullPrompt);
        const response = await result.response;
        const text = response.text();
        
        console.log('✅ Réponse générée:', text.substring(0, 100) + '...');
        return text;
        
    } catch (error) {
        console.error('❌ Erreur API Gemini:', error);
        
        // Gestion des erreurs spécifiques
        if (error.message.includes('API_KEY')) {
            throw new Error('Clé API Gemini invalide ou manquante');
        } else if (error.message.includes('quota')) {
            throw new Error('Quota API Gemini dépassé');
        } else if (error.message.includes('safety')) {
            throw new Error('Contenu bloqué par les filtres de sécurité');
        } else {
            throw new Error('Erreur lors de l\'appel à l\'API Gemini');
        }
    }
}

// Route principale pour le chat
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        // Validation de base
        if (!message || typeof message !== 'string' || message.trim().length === 0) {
            return res.status(400).json({ 
                success: false,
                response: "Message vide ou invalide reçu." 
            });
        }
        
        // Vérifier la longueur du message
        if (message.length > 2000) {
            return res.status(400).json({ 
                success: false,
                response: "Message trop long. Veuillez limiter votre question à 2000 caractères." 
            });
        }
        
        console.log('🔍 Traitement de la question:', message.substring(0, 100) + '...');
        
        // Appeler l'API Gemini
        const geminiResponse = await fetchGeminiResponse(message);
        
        // Réponse réussie
        res.json({ 
            success: true,
            response: geminiResponse,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('❌ Erreur dans /api/chat:', error);
        
        // Réponse d'erreur
        res.status(500).json({ 
            success: false,
            response: error.message || "Une erreur est survenue lors du traitement de votre question.",
            timestamp: new Date().toISOString()
        });
    }
});

// Route de test
app.get('/api/test', (req, res) => {
    res.json({ 
        success: true,
        message: 'Serveur Node.js pour l\'assistant juridique opérationnel',
        timestamp: new Date().toISOString(),
        geminiConfigured: !!process.env.GEMINI_API_KEY
    });
});

// Route de santé
app.get('/health', (req, res) => {
    res.json({ 
        status: 'OK',
        service: 'Legal Assistant API',
        timestamp: new Date().toISOString()
    });
});

// Middleware de gestion d'erreurs globales
app.use((error, req, res, next) => {
    console.error('❌ Erreur globale:', error);
    res.status(500).json({
        success: false,
        message: 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
    });
});

// Middleware pour les routes non trouvées
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route non trouvée',
        timestamp: new Date().toISOString()
    });
});

// Démarrer le serveur
app.listen(PORT, () => {
    console.log(`🚀 Serveur Node.js démarré sur le port ${PORT}`);
    console.log(`📡 API disponible sur http://localhost:${PORT}/api/chat`);
    console.log(`🔑 Clé Gemini configurée: ${process.env.GEMINI_API_KEY ? 'Oui' : 'Non'}`);
    
    if (!process.env.GEMINI_API_KEY) {
        console.warn('⚠️  ATTENTION: Clé API Gemini manquante. Ajoutez GEMINI_API_KEY dans votre fichier .env');
    }
});

module.exports = app;
