<?php

// Script pour vérifier la configuration de l'application

echo "Vérification de la configuration de l'application...\n";

// Vérifier si le fichier .env existe
if (!file_exists('.env')) {
    echo "Erreur: Le fichier '.env' n'existe pas.\n";
    exit(1);
}

// Vérifier si la clé d'application est définie
$env = file_get_contents('.env');
if (!preg_match('/APP_KEY=/', $env)) {
    echo "Erreur: La clé d'application n'est pas définie dans le fichier '.env'.\n";
    echo "Exécutez la commande 'php artisan key:generate' pour générer une clé.\n";
    exit(1);
}

// Vérifier la configuration de la base de données
if (!preg_match('/DB_CONNECTION=/', $env)) {
    echo "Erreur: La configuration de la base de données n'est pas définie dans le fichier '.env'.\n";
    exit(1);
}

// Vérifier si les migrations ont été exécutées
echo "Vérification des migrations...\n";
echo "Exécutez la commande 'php artisan migrate:status' pour vérifier l'état des migrations.\n";

// Vérifier si le lien symbolique pour le stockage a été créé
if (!is_link('public/storage')) {
    echo "Le lien symbolique pour le stockage n'existe pas.\n";
    echo "Exécutez la commande 'php artisan storage:link' pour créer le lien.\n";
}

// Vérifier si le dossier bootstrap/cache est accessible en écriture
if (!is_writable('bootstrap/cache')) {
    echo "Erreur: Le dossier 'bootstrap/cache' n'est pas accessible en écriture.\n";
    exit(1);
}

echo "Vérification de la configuration terminée.\n";
echo "Assurez-vous que toutes les erreurs ont été corrigées avant de continuer.\n";
