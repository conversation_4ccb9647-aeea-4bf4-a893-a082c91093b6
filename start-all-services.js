const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage de tous les services...\n');

// Fonction pour démarrer un processus
function startProcess(command, args, name, color) {
    const process = spawn(command, args, {
        stdio: 'pipe',
        shell: true,
        cwd: __dirname
    });

    // Couleurs pour les logs
    const colors = {
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m',
        reset: '\x1b[0m'
    };

    const colorCode = colors[color] || colors.reset;

    process.stdout.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        lines.forEach(line => {
            console.log(`${colorCode}[${name}]${colors.reset} ${line}`);
        });
    });

    process.stderr.on('data', (data) => {
        const lines = data.toString().split('\n').filter(line => line.trim());
        lines.forEach(line => {
            console.log(`${colorCode}[${name}]${colors.reset} ${line}`);
        });
    });

    process.on('close', (code) => {
        console.log(`${colorCode}[${name}]${colors.reset} Processus terminé avec le code ${code}`);
    });

    return process;
}

// Vérifier si les dépendances sont installées
const fs = require('fs');
if (!fs.existsSync('node_modules')) {
    console.log('📦 Installation des dépendances Node.js...');
    const npmInstall = spawn('npm', ['install'], { stdio: 'inherit', shell: true });
    
    npmInstall.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dépendances installées avec succès\n');
            startAllServices();
        } else {
            console.error('❌ Erreur lors de l\'installation des dépendances');
            process.exit(1);
        }
    });
} else {
    startAllServices();
}

function startAllServices() {
    console.log('🔧 Démarrage des services...\n');

    // 1. Démarrer le serveur Node.js
    console.log('🟢 Démarrage du serveur Node.js (port 3000)...');
    const nodeServer = startProcess('node', ['server.js'], 'NODE', 'green');

    // 2. Démarrer le serveur Laravel
    console.log('🔵 Démarrage du serveur Laravel (port 8000)...');
    const laravelServer = startProcess('php', ['artisan', 'serve'], 'LARAVEL', 'blue');

    // 3. Optionnel: Démarrer le serveur Angular (si présent)
    if (fs.existsSync('angular.json')) {
        console.log('🟠 Démarrage du serveur Angular (port 4200)...');
        const angularServer = startProcess('ng', ['serve'], 'ANGULAR', 'yellow');
    }

    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt de tous les services...');
        
        if (nodeServer) nodeServer.kill();
        if (laravelServer) laravelServer.kill();
        
        setTimeout(() => {
            console.log('✅ Tous les services ont été arrêtés');
            process.exit(0);
        }, 2000);
    });

    // Afficher les informations de connexion après un délai
    setTimeout(() => {
        console.log('\n📡 Services disponibles:');
        console.log('   🟢 API Node.js/Gemini: http://localhost:3000/api/chat');
        console.log('   🔵 API Laravel: http://localhost:8000/api');
        console.log('   🟠 Frontend Angular: http://localhost:4200 (si disponible)');
        console.log('\n🧪 Pour tester l\'API: npm run test:api');
        console.log('🛑 Pour arrêter: Ctrl+C\n');
    }, 3000);
}
