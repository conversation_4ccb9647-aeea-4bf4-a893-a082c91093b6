<?php

namespace App\Http\Controllers;
use App\Models\ConseilJuridique;

use Illuminate\Http\Request;

class ConseilJuridiqueController extends Controller
{
    public function generateAdvice(Request $request)
    {
        $request->validate([
            'question' => 'required|string',
        ]);

        $advice = $this->getLegalAdvice($request->input('question'));

        $conseil = ConseilJuridique::create([
            'question' => $request->input('question'),
            'advice' => $advice,
        ]);
        return response()->json($conseil);

    }

    private function getLegalAdvice($question)
    {
        // Appeler le modèle NLP pour générer le conseil (simulation ici)
        return "Voici le conseil juridique automatisé pour : " . $question;
    }





}