<?php

namespace App\Http\Controllers;

use App\Models\Plainte;
use App\Models\User;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PlainteController extends Controller
{
    public function index()
    {
        $plaintes = Plainte::with(['user', 'assignedTo'])->get();
        return response()->json($plaintes);
    }

    public function store(Request $request)
    {
        // Debug: Logguez la requête entrante
        Log::info('Requête de plainte reçue:', $request->all());
        Log::info('Fichiers reçus:', $request->hasFile('attachments') ? ['count' => count($request->file('attachments'))] : ['aucun fichier']);
        Log::info('Content-Type: ' . $request->header('Content-Type'));

        // Validation avec messages d'erreur personnalisés
        $messages = [
            'user_id.required' => 'L\'identifiant de l\'utilisateur est requis.',
            'user_id.exists' => 'L\'utilisateur spécifié n\'existe pas.',
            'type.required' => 'Le type de plainte est requis.',
            'type.in' => 'Le type de plainte doit être l\'un des suivants: contract, consumer, property, labor, family.',
            'subject.required' => 'Le sujet de la plainte est requis.',
            'subject.max' => 'Le sujet ne doit pas dépasser 200 caractères.',
            'description.required' => 'La description de la plainte est requise.',
            'incident_date.date' => 'La date de l\'incident doit être une date valide.',
            'attachments.*.file' => 'Les pièces jointes doivent être des fichiers valides.',
            'attachments.*.max' => 'Les pièces jointes ne doivent pas dépasser 10 Mo.',
            'attachments.*.mimes' => 'Les pièces jointes doivent être de type: pdf, doc, docx, jpg, jpeg, png.',
        ];

        try {
            $validated = $request->validate([
                'user_id' => 'required|exists:users,id',
                'type' => 'required|in:contract,consumer,property,labor,family',
                'subject' => 'required|string|max:200',
                'description' => 'required|string',
                'incident_date' => 'nullable|date',
                'attachments.*' => 'nullable|file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png', // Max 10MB par fichier
            ], $messages);

            // Vérifier les limites de téléchargement PHP
            $maxFileSize = ini_get('upload_max_filesize');
            $maxPostSize = ini_get('post_max_size');
            Log::info("Limites PHP: upload_max_filesize=$maxFileSize, post_max_size=$maxPostSize");

            // Assigner automatiquement un professionnel
            $professional = $this->assignProfessional($validated['type']);

            // Création de la plainte avec transaction DB pour assurer l'intégrité
            \DB::beginTransaction();

            $plainte = Plainte::create([
                'user_id' => $validated['user_id'],
                'type' => $validated['type'],
                'subject' => $validated['subject'],
                'description' => $validated['description'],
                'incident_date' => $validated['incident_date'] ?? null,
                'status' => 'nouvelle',
                'assigned_to_type' => $professional['type'],
                'assigned_to_id' => $professional['id']
            ]);

            Log::info('Plainte créée avec ID: ' . $plainte->id);

            // Gestion des documents
            if ($request->hasFile('attachments')) {
                Log::info('Traitement des pièces jointes...');

                foreach ($request->file('attachments') as $index => $file) {
                    // Vérifier si le fichier est valide
                    if (!$file->isValid()) {
                        Log::error('Fichier invalide: ' . $file->getClientOriginalName() . ', Erreur: ' . $file->getError());
                        continue;
                    }

                    try {
                        // Générer un nom de fichier unique
                        $fileName = time() . '_' . $index . '_' . $file->getClientOriginalName();

                        // Stocker le fichier dans le dossier documents
                        $path = $file->storeAs('documents', $fileName, 'public');

                        if (!$path) {
                            Log::error('Échec du stockage du fichier: ' . $fileName);
                            continue;
                        }

                        // Créer l'entrée dans la base de données
                        Document::create([
                            'plainte_id' => $plainte->id,
                            'filename' => $file->getClientOriginalName(),
                            'path' => $path,
                            'mime_type' => $file->getMimeType(),
                            'size' => $file->getSize()
                        ]);

                        Log::info('Document ajouté: ' . $fileName);
                    } catch (\Exception $e) {
                        Log::error('Erreur lors du traitement du fichier: ' . $e->getMessage());
                    }
                }
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'plainte' => $plainte->load(['assignedTo', 'documents']),
                'message' => 'Plainte créée et assignée à ' . $professional['name']
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation échouée: ' . json_encode($e->errors()));
            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // En cas d'erreur, annuler la transaction
            if (isset($plainte) && \DB::transactionLevel() > 0) {
                \DB::rollBack();
            }

            Log::error('Erreur création plainte: ' . $e->getMessage());
            Log::error('Trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la création de la plainte.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Méthode pour permettre aux utilisateurs non authentifiés de soumettre des plaintes
     */
    public function storePublic(Request $request)
    {
        // Debug: Logguez la requête entrante
        Log::info('Requête de plainte publique reçue:', $request->all());
        Log::info('Fichiers reçus:', $request->hasFile('attachments') ? ['count' => count($request->file('attachments'))] : ['aucun fichier']);
        Log::info('Content-Type: ' . $request->header('Content-Type'));

        // Validation avec messages d'erreur personnalisés
        $messages = [
            'name.required' => 'Votre nom est requis.',
            'email.required' => 'Votre email est requis.',
            'email.email' => 'Veuillez fournir un email valide.',
            'cin.required' => 'Votre CIN est requis.',
            'phone.required' => 'Votre numéro de téléphone est requis.',
            'type.required' => 'Le type de plainte est requis.',
            'type.in' => 'Le type de plainte doit être l\'un des suivants: contract, consumer, property, labor, family.',
            'subject.required' => 'Le sujet de la plainte est requis.',
            'subject.max' => 'Le sujet ne doit pas dépasser 200 caractères.',
            'description.required' => 'La description de la plainte est requise.',
            'incident_date.date' => 'La date de l\'incident doit être une date valide.',
            'attachments.*.file' => 'Les pièces jointes doivent être des fichiers valides.',
            'attachments.*.max' => 'Les pièces jointes ne doivent pas dépasser 10 Mo.',
            'attachments.*.mimes' => 'Les pièces jointes doivent être de type: pdf, doc, docx, jpg, jpeg, png.',
        ];

        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'cin' => 'required|string|max:20',
                'phone' => 'required|string|max:20',
                'type' => 'required|in:contract,consumer,property,labor,family',
                'subject' => 'required|string|max:200',
                'description' => 'required|string',
                'incident_date' => 'nullable|date',
                'attachments.*' => 'nullable|file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png', // Max 10MB par fichier
            ], $messages);

            // Vérifier si l'utilisateur existe déjà ou en créer un nouveau
            $user = User::where('email', $validated['email'])->first();

            if (!$user) {
                // Créer un nouvel utilisateur avec un mot de passe aléatoire
                $user = User::create([
                    'name' => $validated['name'],
                    'email' => $validated['email'],
                    'cin' => $validated['cin'],
                    'phone' => $validated['phone'],
                    'password' => \Hash::make(\Str::random(12)),
                    'role' => 'citoyen',
                ]);

                Log::info('Nouvel utilisateur créé pour la plainte publique: ' . $user->id);
            }

            // Assigner automatiquement un professionnel
            $professional = $this->assignProfessional($validated['type']);

            // Création de la plainte avec transaction DB pour assurer l'intégrité
            \DB::beginTransaction();

            $plainte = Plainte::create([
                'user_id' => $user->id,
                'type' => $validated['type'],
                'subject' => $validated['subject'],
                'description' => $validated['description'],
                'incident_date' => $validated['incident_date'] ?? null,
                'status' => 'nouvelle',
                'assigned_to_type' => $professional['type'],
                'assigned_to_id' => $professional['id']
            ]);

            Log::info('Plainte publique créée avec ID: ' . $plainte->id);

            // Gestion des documents
            if ($request->hasFile('attachments')) {
                Log::info('Traitement des pièces jointes...');

                foreach ($request->file('attachments') as $index => $file) {
                    // Vérifier si le fichier est valide
                    if (!$file->isValid()) {
                        Log::error('Fichier invalide: ' . $file->getClientOriginalName() . ', Erreur: ' . $file->getError());
                        continue;
                    }

                    try {
                        // Générer un nom de fichier unique
                        $fileName = time() . '_' . $index . '_' . $file->getClientOriginalName();

                        // Stocker le fichier dans le dossier documents
                        $path = $file->storeAs('documents', $fileName, 'public');

                        if (!$path) {
                            Log::error('Échec du stockage du fichier: ' . $fileName);
                            continue;
                        }

                        // Créer l'entrée dans la base de données
                        Document::create([
                            'plainte_id' => $plainte->id,
                            'filename' => $file->getClientOriginalName(),
                            'path' => $path,
                            'mime_type' => $file->getMimeType(),
                            'size' => $file->getSize()
                        ]);

                        Log::info('Document ajouté: ' . $fileName);
                    } catch (\Exception $e) {
                        Log::error('Erreur lors du traitement du fichier: ' . $e->getMessage());
                    }
                }
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Votre plainte a été enregistrée avec succès et sera traitée par nos équipes.',
                'reference' => $plainte->id
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation échouée pour plainte publique: ' . json_encode($e->errors()));
            return response()->json([
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // En cas d'erreur, annuler la transaction
            if (isset($plainte) && \DB::transactionLevel() > 0) {
                \DB::rollBack();
            }

            Log::error('Erreur création plainte publique: ' . $e->getMessage());
            Log::error('Trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la création de la plainte.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function assignProfessional(string $type): array
    {
        // Logique d'assignation
        if (in_array($type, ['contract', 'property'])) {
            $huissier = User::where('role', 'huissier')
                          ->where('is_available', true)
                          ->inRandomOrder()
                          ->firstOrFail();

            return [
                'type' => 'App\Models\User', // Le namespace complet de votre modèle User
                'id' => $huissier->id,
                'name' => $huissier->name
            ];
        } else {
            $avocat = User::where('role', 'avocat')
                         ->where('is_available', true)
                         ->where('specialty', $type)
                         ->inRandomOrder()
                         ->firstOrFail();

            return [
                'type' => 'App\Models\User',
                'id' => $avocat->id,
                'name' => $avocat->name
            ];
        }
    }

    // ... (gardez vos méthodes update et destroy existantes)
}

