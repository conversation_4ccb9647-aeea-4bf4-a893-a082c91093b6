<?php

// <PERSON>ript pour tester l'API

echo "Test de l'API...\n";

// Fonction pour effectuer une requête HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $curl = curl_init();
    
    $options = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => $method,
    ];
    
    if ($data) {
        if (is_array($data)) {
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
            $headers[] = 'Content-Type: application/json';
        } else {
            $options[CURLOPT_POSTFIELDS] = $data;
        }
    }
    
    if (!empty($headers)) {
        $options[CURLOPT_HTTPHEADER] = $headers;
    }
    
    curl_setopt_array($curl, $options);
    
    $response = curl_exec($curl);
    $err = curl_error($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    curl_close($curl);
    
    if ($err) {
        return [
            'success' => false,
            'error' => $err,
            'code' => $httpCode
        ];
    } else {
        return [
            'success' => true,
            'data' => json_decode($response, true),
            'code' => $httpCode
        ];
    }
}

// URL de base de l'API
$baseUrl = 'http://localhost:8000/api';

// Test 1: Vérifier si l'API est accessible
echo "Test 1: Vérifier si l'API est accessible...\n";
$response = makeRequest("$baseUrl/test");
if ($response['success'] && $response['code'] == 200) {
    echo "✅ L'API est accessible.\n";
    echo "Réponse: " . json_encode($response['data']) . "\n";
} else {
    echo "❌ L'API n'est pas accessible.\n";
    echo "Code: " . $response['code'] . "\n";
    if (isset($response['error'])) {
        echo "Erreur: " . $response['error'] . "\n";
    }
    if (isset($response['data'])) {
        echo "Réponse: " . json_encode($response['data']) . "\n";
    }
}

// Test 2: Tester l'inscription
echo "\nTest 2: Tester l'inscription...\n";
$userData = [
    'name' => 'Test User',
    'email' => 'test' . time() . '@example.com',
    'password' => 'password123',
    'password_confirmation' => 'password123',
    'cin' => 'CIN' . time(),
    'phone' => '0612345678',
    'role' => 'citoyen'
];
$response = makeRequest("$baseUrl/register", 'POST', $userData);
if ($response['success'] && $response['code'] == 201) {
    echo "✅ Inscription réussie.\n";
    echo "Réponse: " . json_encode($response['data']) . "\n";
    
    // Récupérer le token pour les tests suivants
    $token = $response['data']['token'] ?? null;
    if ($token) {
        echo "Token récupéré: $token\n";
    }
} else {
    echo "❌ Échec de l'inscription.\n";
    echo "Code: " . $response['code'] . "\n";
    if (isset($response['error'])) {
        echo "Erreur: " . $response['error'] . "\n";
    }
    if (isset($response['data'])) {
        echo "Réponse: " . json_encode($response['data']) . "\n";
    }
}

// Test 3: Tester la soumission d'une plainte publique
echo "\nTest 3: Tester la soumission d'une plainte publique...\n";
$plainteData = [
    'name' => 'Test User',
    'email' => 'test' . time() . '@example.com',
    'cin' => 'CIN' . time(),
    'phone' => '0612345678',
    'type' => 'contract',
    'subject' => 'Test de plainte',
    'description' => 'Ceci est un test de soumission de plainte.',
    'incident_date' => date('Y-m-d')
];
$response = makeRequest("$baseUrl/public/plaintes", 'POST', $plainteData);
if ($response['success'] && $response['code'] == 201) {
    echo "✅ Soumission de plainte publique réussie.\n";
    echo "Réponse: " . json_encode($response['data']) . "\n";
} else {
    echo "❌ Échec de la soumission de plainte publique.\n";
    echo "Code: " . $response['code'] . "\n";
    if (isset($response['error'])) {
        echo "Erreur: " . $response['error'] . "\n";
    }
    if (isset($response['data'])) {
        echo "Réponse: " . json_encode($response['data']) . "\n";
    }
}

echo "\nTests terminés.\n";
