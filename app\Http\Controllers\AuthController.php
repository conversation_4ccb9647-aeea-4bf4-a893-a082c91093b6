<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    // ✅ Inscription
    public function register(Request $request)
    {
        if ($request->isMethod('OPTIONS')) {
            return $this->corsResponse();
        }

        // Debug: Logguez la requête entrante
        \Log::info('Requête d\'inscription reçue:', $request->all());

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'cin' => 'required|string|unique:users',
            'phone' => 'required|string',
            'role' => 'required|string'
        ]);

        if ($validator->fails()) {
            \Log::warning('Validation échouée:', ['errors' => $validator->errors()->toArray()]);
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'cin' => $request->cin,
                'phone' => $request->phone,
                'role' => $request->role,
            ]);

            $token = $user->createToken('authToken')->plainTextToken;

            \Log::info('Utilisateur créé avec succès:', ['user_id' => $user->id]);
            return response()->json([
                'user' => $user,
                'token' => $token
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de l\'inscription:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Une erreur est survenue lors de l\'inscription. Veuillez réessayer.'], 500);
        }
    }

    // ✅ Connexion
    public function login(Request $request)
    {
        if ($request->isMethod('OPTIONS')) {
            return $this->corsResponse();
        }

        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required'
        ]);

        $user = User::where('email', $credentials['email'])->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            return response()->json(['message' => 'Identifiants invalides'], 401);
        }

        $token = $user->createToken('authToken')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token
        ], 200);
    }

    // ✅ Déconnexion (facultatif : à appeler avec Sanctum)
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json(['message' => 'Déconnecté avec succès.']);
    }

    // ✅ Réponse CORS standard
    private function corsResponse()
    {
        return response()->json([], 200)
            ->header('Access-Control-Allow-Origin', 'http://localhost:4200')
            ->header('Access-Control-Allow-Methods', 'POST, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, X-CSRF-TOKEN, Authorization')
            ->header('Access-Control-Allow-Credentials', 'true');
    }
}
