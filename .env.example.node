# Configuration pour le serveur Node.js
NODE_PORT=3000
NODE_ENV=development

# Clé API Google Gemini
# Obtenez votre clé sur: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Configuration CORS
FRONTEND_URL=http://localhost:4200
ALLOWED_ORIGINS=http://localhost:4200,http://localhost:8000

# Configuration de l'API légale (pour Laravel)
NODE_API_URL=http://localhost:3000
LEGAL_API_TIMEOUT=30
LEGAL_API_RETRIES=3

# Configuration de logging
LOG_LEVEL=info
