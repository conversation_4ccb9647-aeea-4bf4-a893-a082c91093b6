<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Cors
{
    public function handle(Request $request, Closure $next)
    {
        $allowedOrigin = 'http://localhost:4200'; // Your frontend URL

        // Log pour le débogage
        \Log::info('CORS Middleware - Request Method: ' . $request->method());
        \Log::info('CORS Middleware - Request Origin: ' . $request->header('Origin'));
        \Log::info('CORS Middleware - Request Headers: ' . json_encode($request->headers->all()));

        // Gestion des requêtes OPTIONS (pré-vol)
        if ($request->isMethod('OPTIONS')) {
            \Log::info('CORS Middleware - Handling OPTIONS request');
            $response = response()->noContent();
        } else {
            $response = $next($request);
        }

        // Headers communs pour toutes les réponses
        $headers = [
            'Access-Control-Allow-Origin' => $allowedOrigin,
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => '*',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Access-Control-Expose-Headers' => 'Authorization, X-Auth-Token, Content-Type, Content-Length, X-Requested-With',
            'Vary' => 'Origin'
        ];

        foreach ($headers as $key => $value) {
            $response->headers->set($key, $value);
        }

        \Log::info('CORS Middleware - Response Headers: ' . json_encode($response->headers->all()));

        return $response;
    }
}
