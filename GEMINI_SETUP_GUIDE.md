# Guide de Configuration de l'API Gemini pour l'Assistant Juridique

## 📋 Prérequis

1. **Node.js** (version 18 ou supérieure)
2. **npm** ou **yarn**
3. **Clé API Google Gemini** (gratuite)
4. **<PERSON><PERSON>** (d<PERSON><PERSON><PERSON> configuré)

## 🔑 Obtenir une Clé API Gemini

1. Allez sur [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Connectez-vous avec votre compte Google
3. Cliquez sur "Create API Key"
4. Copiez la clé générée

## ⚙️ Configuration

### 1. Variables d'environnement

Ajoutez ces variables à votre fichier `.env` Laravel :

```env
# API Node.js
NODE_API_URL=http://localhost:3000

# Clé API Gemini
GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. Installation des dépendances Node.js

```bash
npm install
```

### 3. Démarrage des services

#### Option A: Démarrer séparément
```bash
# Terminal 1: Serveur <PERSON>
php artisan serve

# Terminal 2: Serveur Node.js
npm run start

# Terminal 3: Frontend Angular (si applicable)
ng serve
```

#### Option B: Démarrer ensemble
```bash
npm run dev:all
```

#### Option C: Script automatique (recommandé)
```bash
node start-all-services.js
```

## 🧪 Tests

### 1. Test du serveur Node.js
```bash
npm run test:api
```

### 2. Test avec curl
```bash
# Test de base
curl -X GET http://localhost:3000/api/test

# Test d'une question juridique
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Quelles sont les conditions pour divorcer en Tunisie ?"}'
```

### 3. Test avec Postman

**URL:** `http://localhost:3000/api/chat`
**Method:** POST
**Headers:** `Content-Type: application/json`
**Body:**
```json
{
  "message": "Je peux divorcer ?"
}
```

## 📡 Endpoints Disponibles

### Node.js API (Port 3000)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/test` | GET | Test de santé |
| `/api/chat` | POST | Chat avec Gemini |
| `/health` | GET | Statut du serveur |

### Laravel API (Port 8000)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/public/conseil` | POST | Conseil juridique public |
| `/api/conseil` | POST | Conseil juridique (auth) |
| `/api/conseil/history` | GET | Historique (auth) |
| `/api/conseil/{id}` | GET | Conseil spécifique |

## 🔧 Dépannage

### Problème: "Clé API invalide"
- Vérifiez que `GEMINI_API_KEY` est correctement définie
- Assurez-vous que la clé est valide sur Google AI Studio

### Problème: "Serveur Node.js inaccessible"
- Vérifiez que le serveur Node.js est démarré sur le port 3000
- Vérifiez les logs avec `npm run dev:node`

### Problème: "Quota dépassé"
- L'API Gemini a des limites de quota
- Attendez ou utilisez une autre clé API

### Problème: "Contenu bloqué"
- Gemini a des filtres de sécurité
- Reformulez la question de manière plus neutre

## 📝 Exemples d'utilisation

### Frontend Angular
```typescript
// Service pour appeler l'API
async askLegalQuestion(question: string) {
  const response = await this.http.post('http://localhost:3000/api/chat', {
    message: question
  }).toPromise();

  return response.response;
}
```

### Frontend avec Laravel
```typescript
// Via l'API Laravel
async askLegalQuestion(question: string) {
  const response = await this.http.post('/api/public/conseil', {
    question: question
  }).toPromise();

  return response.conseil.response;
}
```

## 🎯 Bonnes Pratiques

1. **Validation côté client** : Limitez la longueur des questions
2. **Gestion d'erreurs** : Affichez des messages d'erreur clairs
3. **Timeout** : Configurez des timeouts appropriés
4. **Logging** : Surveillez les logs pour détecter les problèmes
5. **Sécurité** : Ne jamais exposer la clé API côté client

## 📊 Monitoring

### Logs Node.js
```bash
# Voir les logs en temps réel
npm run dev:node
```

### Logs Laravel
```bash
# Voir les logs Laravel
tail -f storage/logs/laravel.log
```

## 🚀 Déploiement

### Variables d'environnement de production
```env
NODE_ENV=production
GEMINI_API_KEY=your_production_key
NODE_API_URL=https://your-domain.com:3000
```

### PM2 pour Node.js
```bash
npm install -g pm2
pm2 start server.js --name "legal-assistant"
pm2 startup
pm2 save
```

## 📞 Support

Si vous rencontrez des problèmes :

1. Vérifiez les logs des deux serveurs
2. Testez avec les scripts fournis
3. Consultez la documentation Google Gemini
4. Vérifiez la configuration CORS
