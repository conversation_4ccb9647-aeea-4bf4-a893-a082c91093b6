import { initializeApp } from "firebase/app";
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";

// Configurer votre application Firebase avec votre SDK
const firebaseConfig = {
  apiKey: "AIzaSyCdDHGGcn7t-96SJQ9a2B7wN5bgqAqRjk0",
  authDomain: "legal-platform1.firebaseapp.com",
  projectId: "legal-platform1",
  storageBucket: "legal-platform1.firebasestorage.app",
  messagingSenderId: "110675126133",
  appId: "1:110675126133:web:89a545f56c4de7c2f7953c",
  measurementId: "G-7NQGY9DHS8"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);

// Fonction pour télécharger un fichier
function uploadFile(file) {
  const storageRef = ref(storage, 'documents/' + file.name);  // Emplacement dans Firebase Storage

  const uploadTask = uploadBytesResumable(storageRef, file);

  // Surveiller l'état du téléchargement
  uploadTask.on('state_changed', 
    (snapshot) => {
      const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
      console.log('Progress: ' + progress + '%');
    }, 
    (error) => {
      console.error('Error uploading file:', error);
    }, 
    () => {
      getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
        console.log('File available at', downloadURL);
        // Vous pouvez ici sauvegarder l'URL dans votre base de données
      });
    }
  );
}

// Exporter la fonction pour pouvoir l'utiliser ailleurs
export { uploadFile };