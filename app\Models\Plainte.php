<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Plainte extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'subject',
        'description',
        'incident_date',
        'status',
        'assigned_to_type',
        'assigned_to_id'
    ];

    protected $casts = [
        'incident_date' => 'date',
    ];

    // Relation avec l'utilisateur (citoyen)
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relation polymorphique avec l'assigné (avocat ou huissier)
    public function assignedTo()
    {
        return $this->morphTo();
    }

    // Relation avec les documents
    public function documents()
    {
        return $this->hasMany(Document::class);
    }
}