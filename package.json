{"private": true, "scripts": {"build": "vite build", "dev": "vite", "start": "node server.js", "dev:node": "nodemon server.js", "test:api": "node test-gemini-api.js", "dev:all": "concurrently \"npm run start\" \"npm run dev\" --names \"node,vite\" --prefix-colors \"blue,green\""}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.16", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "nodemon": "^3.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^6.0.11"}, "dependencies": {"@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase": "^11.5.0"}}